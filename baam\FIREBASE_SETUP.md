# Firebase Setup Guide

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Click "Create a project" or "Add project"
3. Enter project name (e.g., "baam-scheduling-app")
4. Enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Enable Firestore Database

1. In your Firebase project, go to **Firestore Database**
2. Click "Create database"
3. Choose **"Start in test mode"** (for development)
4. Select a location (choose closest to your users)
5. Click "Done"

## Step 3: Generate Service Account Key

1. Go to **Project Settings** (gear icon ⚙️)
2. Click **"Service accounts"** tab
3. Click **"Generate new private key"**
4. Download the JSON file
5. **IMPORTANT**: Keep this file secure and never commit it to git

## Step 4: Configure Environment

1. Move the downloaded JSON file to a secure location:
```bash
mkdir -p ~/.config/firebase
mv ~/Downloads/your-project-*-firebase-adminsdk-*.json ~/.config/firebase/baam-service-account.json
```

2. Update your `.env` file:
```bash
FIREBASE_SERVICE_ACCOUNT_KEY_PATH=/Users/<USER>/.config/firebase/baam-service-account.json
OPENAI_API_KEY=your_openai_api_key_here
FLASK_ENV=development
FLASK_DEBUG=True
```

## Step 5: Initialize Database

Run the setup script to create collections and sample data:

```bash
# Make sure you're in the project directory and virtual environment is active
cd /Users/<USER>/CascadeProjects/todolist-api
source venv/bin/activate

# Run the database setup script
python3 setup_firestore.py
```

## Step 6: Create Firestore Indexes

The setup script will show you which indexes to create. Go to:
**Firebase Console > Firestore Database > Indexes > Create Index**

Create these composite indexes:

### Index 1: Tasks by User and Status
- Collection: `tasks`
- Fields: `uid` (Ascending), `status` (Ascending)

### Index 2: Goals by User and Status  
- Collection: `goals`
- Fields: `uid` (Ascending), `status` (Ascending)

### Index 3: Conversations by User and Time
- Collection: `conversations` 
- Fields: `uid` (Ascending), `timestamp` (Descending)

### Index 4: AI Insights by User and Category
- Collection: `ai_insights`
- Fields: `uid` (Ascending), `task_category` (Ascending)

## Step 7: Test Your Setup

1. Start your Flask server:
```bash
python3 app.py
```

2. Test with the sample data:
```bash
# Get sample user
curl http://localhost:5001/api/users/demo_user_123

# Get sample user's goals
curl http://localhost:5001/api/users/demo_user_123/goals

# Get sample user's tasks
curl http://localhost:5001/api/users/demo_user_123/tasks
```

## Security Rules (Production)

For production, update Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /goals/{goalId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.uid;
    }
    
    match /tasks/{taskId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.uid;
    }
    
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.uid;
    }
    
    match /ai_insights/{insightId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.uid;
    }
    
    match /schedule_optimizations/{optimizationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.uid;
    }
  }
}
```

## Troubleshooting

### Common Issues:

1. **"Permission denied" errors**
   - Check that your service account key path is correct
   - Ensure the JSON file has proper permissions

2. **"Collection not found" errors**
   - Run the setup script to initialize collections
   - Check Firestore console to verify collections exist

3. **"Index required" errors**
   - Create the composite indexes as specified above
   - Wait a few minutes for indexes to build

4. **Import errors**
   - Make sure all dependencies are installed: `pip install -r requirements.txt`
   - Activate your virtual environment: `source venv/bin/activate`

### Getting Help:

- Firebase Documentation: https://firebase.google.com/docs/firestore
- Firestore Python SDK: https://firebase.google.com/docs/firestore/quickstart#python
