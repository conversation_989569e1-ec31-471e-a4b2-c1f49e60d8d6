#!/usr/bin/env python3
"""
Chat Actions and Response Formatting for Frontend Integration
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from enum import Enum

class ActionType(str, Enum):
    """Types of actions the AI can suggest"""
    CREATE_TASK = "create_task"
    UPDATE_TASK = "update_task"
    DELETE_TASK = "delete_task"
    SCHEDULE_TASK = "schedule_task"
    RESCHEDULE_TASK = "reschedule_task"
    CREATE_GOAL = "create_goal"
    UPDATE_GOAL = "update_goal"
    MARK_COMPLETE = "mark_complete"
    SET_REMINDER = "set_reminder"
    BULK_RESCHEDULE = "bulk_reschedule"

class ActionStatus(str, Enum):
    """Status of proposed actions"""
    PENDING_APPROVAL = "pending_approval"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXECUTED = "executed"

class ChatAction:
    """Represents a single action that can be taken"""
    
    def __init__(self, action_type: ActionType, description: str, 
                 data: Dict[str, Any], confidence: float = 1.0):
        self.action_id = f"{action_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        self.action_type = action_type
        self.description = description
        self.data = data
        self.confidence = confidence
        self.status = ActionStatus.PENDING_APPROVAL
        self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "action_id": self.action_id,
            "action_type": self.action_type.value,
            "description": self.description,
            "data": self.data,
            "confidence": self.confidence,
            "status": self.status.value,
            "created_at": self.created_at.isoformat()
        }

class ChatResponse:
    """Structured response format for chat interactions"""
    
    def __init__(self, message: str, actions: List[ChatAction] = None, 
                 context: Dict[str, Any] = None):
        self.message = message
        self.actions = actions or []
        self.context = context or {}
        self.response_id = f"chat_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        self.timestamp = datetime.now()
    
    def add_action(self, action: ChatAction):
        """Add an action to the response"""
        self.actions.append(action)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "response_id": self.response_id,
            "message": self.message,
            "actions": [action.to_dict() for action in self.actions],
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "requires_approval": len(self.actions) > 0
        }

class ChatActionBuilder:
    """Helper class to build common chat actions"""
    
    @staticmethod
    def create_task_action(title: str, description: str = "", 
                          estimated_duration: int = 60, 
                          priority: str = "medium",
                          due_date: Optional[str] = None,
                          flexibility: int = 5) -> ChatAction:
        """Create a task creation action"""
        return ChatAction(
            action_type=ActionType.CREATE_TASK,
            description=f"Create new task: {title}",
            data={
                "title": title,
                "description": description,
                "estimated_duration": estimated_duration,
                "priority": priority,
                "due_date": due_date,
                "flexibility": flexibility
            }
        )
    
    @staticmethod
    def schedule_task_action(task_id: str, task_title: str, 
                           scheduled_time: str, 
                           moved_tasks: List[Dict] = None) -> ChatAction:
        """Create a task scheduling action"""
        description = f"Schedule '{task_title}' for {scheduled_time}"
        if moved_tasks:
            description += f" (will move {len(moved_tasks)} other tasks)"
        
        return ChatAction(
            action_type=ActionType.SCHEDULE_TASK,
            description=description,
            data={
                "task_id": task_id,
                "scheduled_time": scheduled_time,
                "moved_tasks": moved_tasks or []
            }
        )
    
    @staticmethod
    def reschedule_task_action(task_id: str, task_title: str,
                             old_time: str, new_time: str,
                             reason: str = "") -> ChatAction:
        """Create a task rescheduling action"""
        description = f"Reschedule '{task_title}' from {old_time} to {new_time}"
        if reason:
            description += f" ({reason})"
        
        return ChatAction(
            action_type=ActionType.RESCHEDULE_TASK,
            description=description,
            data={
                "task_id": task_id,
                "old_time": old_time,
                "new_time": new_time,
                "reason": reason
            }
        )
    
    @staticmethod
    def bulk_reschedule_action(changes: List[Dict[str, Any]], 
                             reason: str = "") -> ChatAction:
        """Create a bulk rescheduling action"""
        description = f"Reschedule {len(changes)} tasks"
        if reason:
            description += f" ({reason})"
        
        return ChatAction(
            action_type=ActionType.BULK_RESCHEDULE,
            description=description,
            data={
                "changes": changes,
                "reason": reason
            }
        )
    
    @staticmethod
    def update_task_action(task_id: str, task_title: str,
                          updates: Dict[str, Any]) -> ChatAction:
        """Create a task update action"""
        update_desc = ", ".join([f"{k}: {v}" for k, v in updates.items()])
        
        return ChatAction(
            action_type=ActionType.UPDATE_TASK,
            description=f"Update '{task_title}': {update_desc}",
            data={
                "task_id": task_id,
                "updates": updates
            }
        )
    
    @staticmethod
    def mark_complete_action(task_id: str, task_title: str) -> ChatAction:
        """Create a task completion action"""
        return ChatAction(
            action_type=ActionType.MARK_COMPLETE,
            description=f"Mark '{task_title}' as completed",
            data={
                "task_id": task_id,
                "completed_at": datetime.now().isoformat()
            }
        )

def create_simple_response(message: str) -> Dict[str, Any]:
    """Create a simple chat response with no actions"""
    response = ChatResponse(message)
    return response.to_dict()

def create_response_with_actions(message: str, actions: List[ChatAction],
                               context: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create a chat response with actions requiring approval"""
    response = ChatResponse(message, actions, context)
    return response.to_dict()

# Example usage functions for common scenarios
def create_scheduling_response(task_title: str, scheduled_time: str, 
                             conflicts: List[Dict] = None,
                             moved_tasks: List[Dict] = None) -> Dict[str, Any]:
    """Create response for scheduling scenarios"""
    
    if not conflicts and not moved_tasks:
        # Simple scheduling
        message = f"I can schedule '{task_title}' for {scheduled_time}. Would you like me to proceed?"
        action = ChatActionBuilder.schedule_task_action("task_id", task_title, scheduled_time)
        return create_response_with_actions(message, [action])
    
    elif moved_tasks:
        # Scheduling with task movements
        message = f"I can schedule '{task_title}' for {scheduled_time}, but I'll need to move {len(moved_tasks)} other flexible tasks. Here's what would change:"
        for moved in moved_tasks:
            message += f"\n• {moved['task_title']}: {moved['old_time']} → {moved['new_time']}"
        message += "\n\nShould I proceed with these changes?"
        
        actions = [ChatActionBuilder.schedule_task_action("task_id", task_title, scheduled_time, moved_tasks)]
        return create_response_with_actions(message, actions)
    
    else:
        # Conflicts that can't be resolved
        message = f"I can't schedule '{task_title}' for {scheduled_time} due to conflicts with:"
        for conflict in conflicts:
            message += f"\n• {conflict['task_title']} ({conflict['time']})"
        message += "\n\nWould you like me to suggest alternative times?"
        return create_simple_response(message)
