# AI-Powered Scheduling App REST API

A Flask-based REST API for an intelligent scheduling application with AI-powered task optimization, duration estimation, and productivity insights.

## Features

### Core Functionality
- **User Management** - Create and manage user profiles
- **Goal Tracking** - Set and track personal/professional goals
- **Task Management** - Create, update, and organize tasks with flexible scheduling
- **AI Chat Interface** - Natural language interaction for schedule management

### AI-Powered Features
- **Schedule Optimization** - AI analyzes and reorders tasks for maximum efficiency
- **Duration Estimation** - Automatic task duration prediction based on content and history
- **Productivity Insights** - Personalized analytics on work patterns and optimal time slots
- **Task Suggestions** - AI-generated task recommendations to achieve goals
- **Smart Prioritization** - Importance scoring based on goals and deadlines

### Technical Features
- Firebase Firestore database integration
- OpenAI GPT-4 integration for AI features
- CORS enabled for frontend integration
- Comprehensive error handling and validation
- RESTful API design with proper HTTP status codes

## API Endpoints

### User Management
- `POST /api/users` - Create a new user
- `GET /api/users/<uid>` - Get user profile
- `PUT /api/users/<uid>` - Update user profile

### Goal Management
- `POST /api/goals` - Create a new goal
- `GET /api/users/<uid>/goals` - Get all goals for a user
- `GET /api/goals/<goal_id>` - Get specific goal
- `PUT /api/goals/<goal_id>` - Update goal
- `DELETE /api/goals/<goal_id>` - Delete goal

### Task Management
- `POST /api/tasks` - Create a new task (with AI duration estimation)
- `GET /api/users/<uid>/tasks` - Get all tasks for a user
- `GET /api/tasks/<task_id>` - Get specific task
- `PUT /api/tasks/<task_id>` - Update task
- `DELETE /api/tasks/<task_id>` - Delete task
- `GET /api/goals/<goal_id>/tasks` - Get tasks for a specific goal

### AI-Powered Features
- `POST /api/users/<uid>/schedule/optimize` - Get AI schedule optimization
- `POST /api/users/<uid>/tasks/suggestions` - Get AI task suggestions
- `GET /api/users/<uid>/insights` - Get productivity insights
- `POST /api/chat` - Chat with AI about scheduling

### Conversations
- `GET /api/users/<uid>/conversations` - Get conversation history

### System
- `GET /api/health` - Health check endpoint

## Setup and Installation

### Prerequisites
- Python 3.8+
- Firebase project with Firestore enabled
- OpenAI API key

### 1. Clone and Setup Environment
```bash
cd /Users/<USER>/CascadeProjects/todolist-api
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Firebase Setup
1. Create a Firebase project at https://console.firebase.google.com
2. Enable Firestore Database
3. Generate a service account key:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file securely

### 3. Environment Configuration
1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` and add your credentials:
```bash
FIREBASE_SERVICE_ACCOUNT_KEY_PATH=/path/to/your/firebase-service-account-key.json
OPENAI_API_KEY=your_openai_api_key_here
FLASK_ENV=development
FLASK_DEBUG=True
```

### 4. Run the Application
```bash
python3 app.py
```

The server will start on `http://localhost:5001`

## API Usage Examples

### Create a User
```bash
curl -X POST http://localhost:5001/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "uid": "user123",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>"
  }'
```

### Create a Goal
```bash
curl -X POST http://localhost:5001/api/goals \
  -H "Content-Type: application/json" \
  -d '{
    "uid": "user123",
    "title": "Learn Python",
    "description": "Master Python programming",
    "category": "learning"
  }'
```

### Create a Task (with AI duration estimation)
```bash
curl -X POST http://localhost:5001/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "uid": "user123",
    "task_name": "Complete Python tutorial",
    "notes": "Work through chapters 1-5",
    "importance": 4,
    "categories": ["learning", "programming"]
  }'
```

### Get AI Schedule Optimization
```bash
curl -X POST http://localhost:5001/api/users/user123/schedule/optimize \
  -H "Content-Type: application/json" \
  -d '{"message": "I have a busy week, please optimize my schedule"}'
```

### Chat with AI
```bash
curl -X POST http://localhost:5001/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "uid": "user123",
    "message": "When should I schedule my workout this week?"
  }'
```

### Get Productivity Insights
```bash
curl http://localhost:5001/api/users/user123/insights
```

## Data Structures

### User
```json
{
  "uid": "user123",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "date_of_birth": "1990-01-01",
  "created_at": "2023-01-01T00:00:00",
  "updated_at": "2023-01-01T00:00:00"
}
```

### Goal
```json
{
  "goal_id": "goal123",
  "uid": "user123",
  "title": "Learn Python",
  "description": "Master Python programming",
  "category": "learning",
  "status": "active",
  "created_at": "2023-01-01T00:00:00",
  "updated_at": "2023-01-01T00:00:00"
}
```

### Task
```json
{
  "task_id": "task123",
  "uid": "user123",
  "goal_id": "goal123",
  "task_name": "Complete Python tutorial",
  "start_time": "2023-01-01T09:00:00",
  "end_time": "2023-01-01T11:00:00",
  "estimated_duration": 120,
  "actual_duration": 135,
  "flexibility": "flexible",
  "importance": 4,
  "notes": "Work through chapters 1-5",
  "status": "planned",
  "categories": ["learning", "programming"],
  "recurrence": null,
  "completed_at": null,
  "created_at": "2023-01-01T00:00:00",
  "updated_at": "2023-01-01T00:00:00"
}
```

## Development Notes

### Architecture
- **Flask** - Web framework
- **Firebase Firestore** - NoSQL database for scalable data storage
- **OpenAI GPT-4** - AI-powered features
- **Pydantic** - Data validation and serialization

### Security Considerations
- Store API keys securely in environment variables
- Implement proper authentication/authorization (Firebase Auth recommended)
- Restrict CORS origins in production
- Validate all user inputs
- Rate limit API endpoints

### Scaling Considerations
- Firebase Firestore handles scaling automatically
- Consider caching for frequently accessed data
- Implement request queuing for AI operations
- Monitor OpenAI API usage and costs

### Future Enhancements
- Real-time notifications for schedule changes
- Calendar integration (Google Calendar, Outlook)
- Team collaboration features
- Mobile app companion
- Advanced analytics dashboard
- Machine learning for better predictions
