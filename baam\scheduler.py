#!/usr/bin/env python3
"""
Real-time AI Scheduling Algorithm for Todolist/Calendar App
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from models import Task
import logging

class SchedulingAlgorithm:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def find_optimal_time(self, new_task: Task, existing_tasks: List[Task], 
                         preferred_time: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Find optimal time slot for a new task given existing schedule.
        
        Returns:
        - success: bool - whether scheduling was possible
        - scheduled_time: datetime - when to schedule the task
        - conflicts: List - any conflicts that needed resolution
        - moved_tasks: List - tasks that were moved to accommodate
        - reasoning: str - explanation of scheduling decision
        """
        
        # Convert tasks to time blocks for easier processing
        time_blocks = self._tasks_to_time_blocks(existing_tasks)
        
        # If user specified a preferred time, try that first
        if preferred_time:
            result = self._try_schedule_at_time(new_task, time_blocks, preferred_time)
            if result['success']:
                return result
        
        # Find available slots based on constraints
        available_slots = self._find_available_slots(
            new_task, time_blocks, 
            start_search=new_task.earliest_start or datetime.now(),
            end_search=new_task.latest_finish or (datetime.now() + timedelta(days=7))
        )
        
        if not available_slots:
            # Try to move flexible tasks to make room
            return self._attempt_task_rearrangement(new_task, existing_tasks)
        
        # Pick the best available slot
        best_slot = self._select_best_slot(available_slots, new_task)
        
        return {
            'success': True,
            'scheduled_time': best_slot,
            'conflicts': [],
            'moved_tasks': [],
            'reasoning': f"Scheduled at {best_slot.strftime('%Y-%m-%d %H:%M')} - optimal available slot"
        }
    
    def _tasks_to_time_blocks(self, tasks: List[Task]) -> List[Dict[str, Any]]:
        """Convert tasks to time blocks for scheduling analysis"""
        blocks = []
        for task in tasks:
            if task.scheduled_time and task.status.value != 'completed':
                end_time = task.scheduled_time + timedelta(minutes=task.estimated_duration)
                blocks.append({
                    'start': task.scheduled_time,
                    'end': end_time,
                    'task': task,
                    'flexibility': task.flexibility
                })
        return sorted(blocks, key=lambda x: x['start'])
    
    def _try_schedule_at_time(self, new_task: Task, time_blocks: List[Dict], 
                             target_time: datetime) -> Dict[str, Any]:
        """Try to schedule task at specific time"""
        end_time = target_time + timedelta(minutes=new_task.estimated_duration)
        
        # Check for conflicts
        conflicts = []
        for block in time_blocks:
            if self._times_overlap(target_time, end_time, block['start'], block['end']):
                conflicts.append(block)
        
        if not conflicts:
            return {
                'success': True,
                'scheduled_time': target_time,
                'conflicts': [],
                'moved_tasks': [],
                'reasoning': f"Scheduled at preferred time {target_time.strftime('%Y-%m-%d %H:%M')}"
            }
        
        # Check if we can move conflicting tasks (if they're flexible enough)
        moveable_conflicts = [c for c in conflicts if c['flexibility'] >= 7]
        
        if len(moveable_conflicts) == len(conflicts):
            # All conflicts are moveable - attempt to reschedule them
            moved_tasks = []
            for conflict in moveable_conflicts:
                # Find new time for conflicting task
                new_time = self._find_alternative_time(conflict['task'], time_blocks, 
                                                     exclude_range=(target_time, end_time))
                if new_time:
                    moved_tasks.append({
                        'task_id': conflict['task'].task_id,
                        'old_time': conflict['start'],
                        'new_time': new_time
                    })
                else:
                    return {
                        'success': False,
                        'reason': f"Cannot move conflicting task: {conflict['task'].title}",
                        'conflicts': conflicts
                    }
            
            return {
                'success': True,
                'scheduled_time': target_time,
                'conflicts': conflicts,
                'moved_tasks': moved_tasks,
                'reasoning': f"Scheduled at preferred time by moving {len(moved_tasks)} flexible tasks"
            }
        
        return {
            'success': False,
            'reason': "Time slot conflicts with inflexible tasks",
            'conflicts': conflicts
        }
    
    def _find_available_slots(self, new_task: Task, time_blocks: List[Dict],
                             start_search: datetime, end_search: datetime) -> List[datetime]:
        """Find all available time slots for the task"""
        available_slots = []
        duration = timedelta(minutes=new_task.estimated_duration)
        
        # Start searching from the earliest allowed time
        current_time = max(start_search, datetime.now())
        
        # Round to next 15-minute interval for cleaner scheduling
        minutes = current_time.minute
        rounded_minutes = ((minutes // 15) + 1) * 15
        if rounded_minutes >= 60:
            current_time = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        else:
            current_time = current_time.replace(minute=rounded_minutes, second=0, microsecond=0)
        
        while current_time + duration <= end_search:
            end_time = current_time + duration
            
            # Check if this slot conflicts with any existing tasks
            has_conflict = any(
                self._times_overlap(current_time, end_time, block['start'], block['end'])
                for block in time_blocks
            )
            
            if not has_conflict and self._is_reasonable_time(current_time):
                available_slots.append(current_time)
            
            # Move to next 15-minute slot
            current_time += timedelta(minutes=15)
        
        return available_slots
    
    def _attempt_task_rearrangement(self, new_task: Task, existing_tasks: List[Task]) -> Dict[str, Any]:
        """Try to rearrange existing flexible tasks to make room for new task"""
        
        # Sort existing tasks by flexibility (most flexible first)
        flexible_tasks = [t for t in existing_tasks if t.flexibility >= 6 and t.status.value != 'completed']
        flexible_tasks.sort(key=lambda x: x.flexibility, reverse=True)
        
        if not flexible_tasks:
            return {
                'success': False,
                'reason': "No available time slots and no flexible tasks to move",
                'suggestions': [
                    "Consider extending the deadline",
                    "Reduce estimated duration",
                    "Make some existing tasks more flexible"
                ]
            }
        
        # Try different combinations of moving flexible tasks
        for num_to_move in range(1, min(4, len(flexible_tasks) + 1)):
            for i in range(len(flexible_tasks) - num_to_move + 1):
                tasks_to_move = flexible_tasks[i:i + num_to_move]
                
                # Simulate removing these tasks temporarily
                remaining_tasks = [t for t in existing_tasks if t not in tasks_to_move]
                
                # Try to schedule the new task
                result = self.find_optimal_time(new_task, remaining_tasks)
                
                if result['success']:
                    # Now try to reschedule the moved tasks
                    moved_tasks = []
                    temp_schedule = remaining_tasks + [new_task]
                    
                    all_rescheduled = True
                    for task in tasks_to_move:
                        reschedule_result = self.find_optimal_time(task, temp_schedule)
                        if reschedule_result['success']:
                            task.scheduled_time = reschedule_result['scheduled_time']
                            temp_schedule.append(task)
                            moved_tasks.append({
                                'task_id': task.task_id,
                                'new_time': reschedule_result['scheduled_time']
                            })
                        else:
                            all_rescheduled = False
                            break
                    
                    if all_rescheduled:
                        return {
                            'success': True,
                            'scheduled_time': result['scheduled_time'],
                            'conflicts': [],
                            'moved_tasks': moved_tasks,
                            'reasoning': f"Rearranged {len(moved_tasks)} flexible tasks to accommodate new task"
                        }
        
        return {
            'success': False,
            'reason': "Unable to rearrange existing tasks to fit new task",
            'suggestions': [
                "Consider scheduling for a later date",
                "Reduce task duration",
                "Increase flexibility of existing tasks"
            ]
        }
    
    def _select_best_slot(self, available_slots: List[datetime], task: Task) -> datetime:
        """Select the best available slot based on task preferences and constraints"""
        
        # Prefer slots closer to preferred working hours (9 AM - 5 PM)
        def score_slot(slot: datetime) -> float:
            hour = slot.hour
            
            # Base score - prefer business hours
            if 9 <= hour <= 17:
                score = 1.0
            elif 8 <= hour <= 18:
                score = 0.8
            elif 7 <= hour <= 19:
                score = 0.6
            else:
                score = 0.3
            
            # Bonus for morning slots (generally more productive)
            if 9 <= hour <= 11:
                score += 0.2
            
            # Penalty for very late or very early
            if hour < 7 or hour > 20:
                score -= 0.5
            
            return max(0, score)
        
        # Score all slots and pick the best
        scored_slots = [(slot, score_slot(slot)) for slot in available_slots]
        scored_slots.sort(key=lambda x: x[1], reverse=True)
        
        return scored_slots[0][0]
    
    def _find_alternative_time(self, task: Task, time_blocks: List[Dict], 
                              exclude_range: Tuple[datetime, datetime]) -> Optional[datetime]:
        """Find alternative time for a task, excluding a specific range"""
        
        # Create a modified search that excludes the given range
        start_search = task.earliest_start or datetime.now()
        end_search = task.latest_finish or (datetime.now() + timedelta(days=7))
        
        available_slots = self._find_available_slots(task, time_blocks, start_search, end_search)
        
        # Filter out slots that overlap with excluded range
        exclude_start, exclude_end = exclude_range
        filtered_slots = []
        
        for slot in available_slots:
            slot_end = slot + timedelta(minutes=task.estimated_duration)
            if not self._times_overlap(slot, slot_end, exclude_start, exclude_end):
                filtered_slots.append(slot)
        
        return filtered_slots[0] if filtered_slots else None
    
    def _times_overlap(self, start1: datetime, end1: datetime, 
                      start2: datetime, end2: datetime) -> bool:
        """Check if two time ranges overlap"""
        return start1 < end2 and start2 < end1
    
    def _is_reasonable_time(self, time: datetime) -> bool:
        """Check if time is within reasonable working hours"""
        hour = time.hour
        # Allow scheduling between 6 AM and 11 PM
        return 6 <= hour <= 23

# Convenience function for API use
def schedule_task(new_task: Task, existing_tasks: List[Task], 
                 preferred_time: Optional[datetime] = None) -> Dict[str, Any]:
    """
    Main function to schedule a task.
    
    Args:
        new_task: Task to be scheduled
        existing_tasks: List of already scheduled tasks
        preferred_time: Optional preferred time for the task
    
    Returns:
        Dict with scheduling result including success status, time, and reasoning
    """
    scheduler = SchedulingAlgorithm()
    return scheduler.find_optimal_time(new_task, existing_tasks, preferred_time)
