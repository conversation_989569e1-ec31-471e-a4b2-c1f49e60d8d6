from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum

class FlexibilityType(str, Enum):
    RIGID = "rigid"
    FLEXIBLE = "flexible"
    ANYTIME = "anytime"

class TaskStatus(str, Enum):
    PLANNED = "planned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"
    OVERDUE = "overdue"

class GoalStatus(str, Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"

class GoalCategory(str, Enum):
    CAREER = "career"
    HEALTH = "health"
    LEARNING = "learning"
    PERSONAL = "personal"
    FINANCE = "finance"
    RELATIONSHIPS = "relationships"

class User(BaseModel):
    uid: str
    first_name: str
    last_name: str
    email: str
    date_of_birth: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "uid": self.uid,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "email": self.email,
            "date_of_birth": self.date_of_birth,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

class Goal(BaseModel):
    goal_id: str
    uid: str
    title: str
    description: str
    category: GoalCategory
    status: GoalStatus = GoalStatus.ACTIVE
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "goal_id": self.goal_id,
            "uid": self.uid,
            "title": self.title,
            "description": self.description,
            "category": self.category.value,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

class Task(BaseModel):
    task_id: Optional[str] = None
    uid: str
    title: str
    description: Optional[str] = ""
    status: TaskStatus = TaskStatus.PLANNED
    priority: str = "medium"  # low, medium, high
    category: Optional[str] = "general"
    
    # Scheduling fields
    due_date: Optional[datetime] = None
    scheduled_time: Optional[datetime] = None  # When task is scheduled to start
    estimated_duration: Optional[int] = 60  # minutes
    actual_duration: Optional[int] = None
    flexibility: int = 5  # 1-10 scale: 1=fixed time, 10=very flexible
    
    # Time constraints
    earliest_start: Optional[datetime] = None  # Can't start before this
    latest_finish: Optional[datetime] = None   # Must finish by this
    
    # Optional goal linking
    goal_id: Optional[str] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "uid": self.uid,
            "title": self.title,
            "description": self.description,
            "status": self.status.value,
            "priority": self.priority,
            "category": self.category,
            "due_date": self.due_date.isoformat() if self.due_date else None,
            "scheduled_time": self.scheduled_time.isoformat() if self.scheduled_time else None,
            "estimated_duration": self.estimated_duration,
            "actual_duration": self.actual_duration,
            "flexibility": self.flexibility,
            "earliest_start": self.earliest_start.isoformat() if self.earliest_start else None,
            "latest_finish": self.latest_finish.isoformat() if self.latest_finish else None,
            "goal_id": self.goal_id,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

class Conversation(BaseModel):
    conversation_id: str
    uid: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    transcript: str
    summary: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "conversation_id": self.conversation_id,
            "uid": self.uid,
            "timestamp": self.timestamp.isoformat(),
            "transcript": self.transcript,
            "summary": self.summary,
            "created_at": self.created_at.isoformat()
        }

class AIInsight(BaseModel):
    insight_id: str
    uid: str
    task_category: str
    average_duration: int  # in minutes
    optimal_time_slots: List[str]  # e.g., ["09:00-11:00", "14:00-16:00"]
    completion_rate: float  # 0.0 to 1.0
    created_at: datetime = Field(default_factory=datetime.utcnow)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "insight_id": self.insight_id,
            "uid": self.uid,
            "task_category": self.task_category,
            "average_duration": self.average_duration,
            "optimal_time_slots": self.optimal_time_slots,
            "completion_rate": self.completion_rate,
            "created_at": self.created_at.isoformat()
        }

# Removed ScheduleOptimization - using real-time scheduling algorithm instead
