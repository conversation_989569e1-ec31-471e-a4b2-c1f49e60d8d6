#!/usr/bin/env python3
"""
Test script for AI-powered endpoints
"""
import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://127.0.0.1:5001"
TEST_UID = "test-user-for-testing"
HEADERS = {"X-Test-Auth-Uid": TEST_UID}

def test_daily_schedule():
    """Test the daily schedule endpoint"""
    print("🧪 Testing daily schedule endpoint...")
    
    today = datetime.now().strftime("%Y-%m-%d")
    
    url = f"{BASE_URL}/api/users/{TEST_UID}/schedule/daily"
    params = {"date": today}
    
    try:
        response = requests.get(url, params=params, headers=HEADERS)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Daily schedule retrieved: {len(data.get('tasks', []))} tasks")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")

def create_test_task():
    """Creates a test task and returns its ID"""
    print("\n🧪 Creating a test task...")
    url = f"{BASE_URL}/api/tasks"
    payload = {
        "title": "Test Task for Scheduling",
        "uid": TEST_UID,
        "description": "A task to test AI scheduling.",
        "estimated_duration": 30,
        "flexibility": 5,
        "due_date": (datetime.now() + timedelta(days=1)).isoformat(),
        "task_name": "Complete project report"
    }
    try:
        response = requests.post(url, json=payload, headers=HEADERS)
        if response.status_code == 201:
            task_id = response.json().get('task_id')
            print(f"✅ Task created with ID: {task_id}")
            return task_id
        else:
            print(f"❌ Failed to create task. Status: {response.status_code}, Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
        return None

def test_task_scheduling():
    """Test AI task scheduling endpoint"""
    print("\n🧪 Testing task scheduling endpoint...")

    test_task_id = create_test_task()
    if not test_task_id:
        print("Skipping scheduling test because task creation failed.")
        return

    url = f"{BASE_URL}/api/tasks/{test_task_id}/schedule"
    
    payload = {
        "preferred_time": (datetime.now() + timedelta(hours=2)).isoformat(),
        "duration": 60,
        "flexibility": 7
    }
    
    try:
        response = requests.post(url, json=payload, headers=HEADERS)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")

def test_chat_endpoint():
    """Test chat endpoint structure (without calling OpenAI)"""
    print("\n🧪 Testing chat endpoint...")
    
    url = f"{BASE_URL}/api/chat"
    payload = {
        "message": "What tasks do I have today?"
    }
    
    try:
        response = requests.post(url, json=payload, headers=HEADERS)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")

def test_server_health():
    """Test basic server health"""
    print("🧪 Testing server health...")
    
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Server is responding")
        else:
            print(f"❌ Server error: {response.text}")
    except Exception as e:
        print(f"❌ Server unreachable: {str(e)}")

if __name__ == "__main__":
    print("🚀 Testing AI Todolist API Endpoints\n")
    
    test_server_health()
    test_daily_schedule()
    test_task_scheduling()
    test_chat_endpoint()
    
    print("\n✅ Endpoint testing completed!")
