import os
import requests
import time
from typing import Dict, List, Any, Optional
import json
from datetime import datetime, timedelta

class OpenAIService:
    def __init__(self):
        if 'OPENAI_API_KEY' not in os.environ:
            print("⚠️  Warning: OPENAI_API_KEY not set. AI features will be disabled.")
            self.api_key = None
        else:
            self.api_key = os.environ['OPENAI_API_KEY']
            if self.api_key and len(self.api_key) > 8:
                print(f"✅ OpenAI API key loaded successfully (ends with '...{self.api_key[-4:]}')")
            else:
                print("✅ OpenAI API key loaded, but it appears to be very short.")
        self.model = "gpt-4o-mini"
        self.base_url = "https://api.openai.com/v1"

    def _make_request(self, data: Dict[str, Any], max_retries: int = 3, backoff_factor: float = 0.5) -> requests.Response:
        """Make a request to the OpenAI API with retries on rate limit errors."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        for i in range(max_retries):
            try:
                response = requests.post(f"{self.base_url}/chat/completions", headers=headers, json=data)
                response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
                return response
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:
                    print(f"Received 429 error. Response: {e.response.text}")
                    wait_time = backoff_factor * (2 ** i)
                    print(f"Retrying in {wait_time:.2f} seconds...")
                    time.sleep(wait_time)
                else:
                    print(f"HTTP Error {e.response.status_code}: {e.response.text}")
                    raise e  # Re-raise other HTTP errors immediately
            except requests.exceptions.RequestException as e:
                print(f"Request failed: {e}. Retrying... ({i+1}/{max_retries})")
                time.sleep(backoff_factor * (2 ** i))
        
        raise Exception(f"API request failed after {max_retries} retries.")

    def analyze_schedule_optimization(self, user_tasks: List[Dict[str, Any]], 
                                    user_goals: List[Dict[str, Any]], 
                                    user_message: str,
                                    ai_insights: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze user's schedule and provide optimization recommendations
        """
        if not self.api_key:
            return {"error": "OpenAI API key not configured. AI features are disabled."}
        system_prompt = """
        You are an AI scheduling assistant that helps users optimize their task schedules.
        
        Your capabilities:
        1. Analyze current tasks and their priorities
        2. Consider user goals and deadlines
        3. Factor in task flexibility and importance
        4. Use historical data to estimate realistic durations
        5. Suggest optimal time slots based on user patterns
        6. Provide clear reasoning for recommendations
        
        Always respond in JSON format with:
        {
            "optimized_schedule": [...], // Array of optimized tasks with suggested times
            "reasoning": "...", // Clear explanation of changes made
            "recommendations": [...], // Additional suggestions
            "estimated_time_savings": "...", // How much time this saves
            "priority_adjustments": [...] // Any priority changes suggested
        }
        """
        
        user_context = {
            "current_tasks": user_tasks,
            "goals": user_goals,
            "user_message": user_message,
            "ai_insights": ai_insights or []
        }
        
        user_prompt = f"Please analyze and optimize my schedule: {json.dumps(user_context)}"
        
        try:
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "max_tokens": 2000,
                "temperature": 0.7
            }
            response = self._make_request(data)
            
            content = response.json()["choices"][0]["message"]["content"]
            return json.loads(content)
            
        except Exception as e:
            print(f"OpenAI API error: {str(e)}")
            return {
                "success": False,
                "message": f"Unable to analyze schedule due to AI service error: {str(e)}",
                "recommendations": [],
                "estimated_time_savings": "0 minutes",
                "priority_adjustments": []
            }

    def estimate_task_duration(self, task_title: str, task_description: str, 
                              task_category: str = None, 
                              historical_data: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Estimate realistic duration for a task based on description and historical data
        """
        if not self.api_key:
            return {"error": "OpenAI API key not configured. AI features are disabled."}
        system_prompt = """
        You are an AI assistant that estimates task durations based on task details and historical data.
        
        Consider:
        1. Task complexity and scope
        2. Historical data on similar tasks
        3. Category-specific patterns
        4. Realistic time estimates (not overly optimistic)
        
        Respond with a JSON object containing:
        {
            "estimated_duration": ..., // in minutes
            "confidence": ..., // 0-1 scale
            "reasoning": "..." // Clear explanation of estimate
        }
        """
        
        context = {
            "task_title": task_title,
            "task_description": task_description,
            "task_category": task_category,
            "historical_data": historical_data or []
        }
        
        try:
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Estimate duration for this task: {json.dumps(context)}"}
                ],
                "temperature": 0.3,
                "max_tokens": 200
            }
            response = self._make_request(data)
            
            content = response.json()["choices"][0]["message"]["content"]
            return json.loads(content)
            
        except Exception as e:
            return {
                "estimated_duration": 60,  # default fallback
                "confidence": 0.5,
                "reasoning": f"Unable to estimate due to AI service error: {str(e)}"
            }

    def suggest_tasks_for_goal(self, goal_title: str, goal_description: str, 
                              current_tasks: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Suggest specific tasks to help achieve a goal
        """
        if not self.api_key:
            return {"error": "OpenAI API key not configured. AI features are disabled."}
        
        system_prompt = """
        You are an AI assistant that suggests actionable tasks to help users achieve their goals.
        
        For the given goal, suggest 3-5 specific, actionable tasks that would move the user closer to completion.
        Consider existing tasks to avoid duplication.
        
        Respond with a JSON object:
        {
            "suggested_tasks": [
                {
                    "title": "...",
                    "description": "...",
                    "priority": "high|medium|low",
                    "estimated_duration": 60,  # in minutes
                    "category": "..."
                }
            ],
            "reasoning": "Why these tasks will help achieve the goal"
        }
        """
        
        context = {
            "goal_title": goal_title,
            "goal_description": goal_description,
            "current_tasks": current_tasks or []
        }
        
        try:
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Suggest tasks for this goal: {json.dumps(context)}"}
                ],
                "temperature": 0.7,
                "max_tokens": 800
            }
            response = self._make_request(data)
            
            content = response.json()["choices"][0]["message"]["content"]
            return json.loads(content)
            
        except Exception as e:
            return {
                "suggested_tasks": [],
                "reasoning": f"Unable to generate suggestions due to AI service error: {str(e)}"
            }

    def generate_task_suggestions(self, user_goals: List[Dict[str, Any]], 
                                user_preferences: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Generate task suggestions to help user achieve their goals
        """
        system_prompt = """
        You are an AI assistant that suggests actionable tasks to help users achieve their goals.
        
        For each goal, suggest 2-3 specific, actionable tasks that would move the user closer to completion.
        
        Respond in JSON format:
        [
            {
                "task_name": "...",
                "description": "...",
                "estimated_duration": 60, // in minutes
                "importance": 4, // 1-5 scale
                "category": "...",
                "goal_id": "...", // which goal this supports
                "suggested_deadline": "..." // ISO format date
            }
        ]
        """
        
        context = {
            "goals": user_goals,
            "preferences": user_preferences or {}
        }
        
        try:
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Generate task suggestions for these goals: {json.dumps(context)}"}
                ],
                "temperature": 0.8,
                "max_tokens": 1500
            }
            response = self._make_request(data)
            
            content = response.json()["choices"][0]["message"]["content"]
            return json.loads(content)
            
        except Exception as e:
            return []

    def analyze_productivity_patterns(self, user_tasks: List[Dict[str, Any]], 
                                    completion_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze user's productivity patterns and provide insights
        """
        if not self.api_key:
            return {"error": "OpenAI API key not configured. AI features are disabled."}
        system_prompt = """
        You are an AI assistant that analyzes productivity patterns from task completion data.
        
        Analyze the data to identify:
        1. Most productive time slots
        2. Task categories that take longer/shorter than estimated
        3. Completion rate patterns
        4. Recommendations for improvement
        
        Respond in JSON format:
        {
            "optimal_time_slots": [...], // e.g., ["09:00-11:00", "14:00-16:00"]
            "category_insights": {...}, // category -> average duration, completion rate
            "productivity_score": 0.85, // 0-1 scale
            "recommendations": [...] // actionable suggestions
        }
        """
        
        try:
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Analyze these tasks and completion history: {json.dumps({'tasks': user_tasks, 'completion_history': completion_history})}"}
                ],
                "temperature": 0.5,
                "max_tokens": 1000
            }
            response = self._make_request(data)
            
            content = response.json()["choices"][0]["message"]["content"]
            return json.loads(content)
            
        except Exception as e:
            return {
                "optimal_time_slots": ["09:00-12:00", "14:00-17:00"],
                "category_insights": {},
                "productivity_score": 0.75,
                "recommendations": ["Unable to analyze due to AI service error"]
            }

    def generate_chat_response(self, user_message: str, conversation_history: List[Dict[str, Any]], 
                             user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate conversational AI response for general scheduling questions
        """
        if not self.api_key:
            return {"error": "OpenAI API key not configured. AI features are disabled."}
        system_prompt = """
        You are a helpful AI scheduling assistant. You help users manage their tasks, goals, and schedule optimization.
        
        You can:
        - Answer questions about their schedule
        - Provide advice on time management
        - Help prioritize tasks
        - Suggest schedule improvements
        - Explain your reasoning clearly
        
        Be conversational, helpful, and concise. If you need more information to help, ask specific questions.
        """
        
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add conversation history
        if conversation_history:
            messages.extend(conversation_history[-10:])  # Keep last 10 messages
        
        # Add current user message with context
        user_content = user_message
        if user_context:
            user_content += f"\n\nContext: {json.dumps(user_context)}"
        
        messages.append({"role": "user", "content": user_content})
        
        try:
            data = {
                "model": self.model,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 500
            }
            response = self._make_request(data)
            
            content = response.json()["choices"][0]["message"]["content"]
            return {"response": content, "success": True}
            
        except Exception as e:
            return {"response": f"I'm sorry, I'm having trouble processing your request right now. Error: {str(e)}", "success": False}
