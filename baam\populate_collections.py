#!/usr/bin/env python3
"""
Script to populate missing Firestore collections so they appear in Firebase Console
"""

import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
from firebase_service import FirebaseService

# Load environment variables
load_dotenv()

def populate_missing_collections():
    """Add sample data to collections that don't appear in Firebase Console"""
    
    firebase_service = FirebaseService()
    
    print("🔄 Populating missing collections...")
    
    # 1. Add sample conversation
    conversation_data = {
        'uid': 'demo_user_123',
        'message': 'Can you help me optimize my schedule for tomorrow?',
        'response': 'I can help you optimize your schedule! Please share your tasks and priorities.',
        'timestamp': datetime.utcnow(),
        'session_id': 'session_001',
        'message_type': 'user_query'
    }
    
    conversation_id = firebase_service.create_conversation(conversation_data)
    print(f"✅ Created sample conversation: {conversation_id}")
    
    # 2. Add sample AI insight
    insight_data = {
        'uid': 'demo_user_123',
        'insight_type': 'productivity_pattern',
        'task_category': 'learning',
        'content': {
            'pattern': 'User is most productive in the morning for learning tasks',
            'confidence': 0.85,
            'recommendation': 'Schedule learning tasks between 9-11 AM'
        },
        'timestamp': datetime.utcnow(),
        'source': 'task_completion_analysis'
    }
    
    insight_id = firebase_service.create_ai_insight(insight_data)
    print(f"✅ Created sample AI insight: {insight_id}")
    
    # 3. Add sample schedule optimization
    optimization_data = {
        'uid': 'demo_user_123',
        'optimization_type': 'daily_schedule',
        'input_message': 'Help me plan my day efficiently',
        'result': {
            'optimized_schedule': [
                {'time': '09:00', 'task': 'Complete Python ML Tutorial', 'duration': 120},
                {'time': '11:00', 'task': 'Review AI Development progress', 'duration': 60}
            ],
            'reasoning': 'Scheduled learning tasks during peak productivity hours',
            'estimated_time_savings': '30 minutes'
        },
        'timestamp': datetime.utcnow(),
        'tasks_analyzed': 2,
        'goals_considered': 1
    }
    
    optimization_id = firebase_service.create_schedule_optimization(optimization_data)
    print(f"✅ Created sample schedule optimization: {optimization_id}")
    
    print("\n🎉 All missing collections now have sample data!")
    print("Check Firebase Console - all 6 collections should now be visible:")
    print("- users ✅")
    print("- goals ✅") 
    print("- tasks ✅")
    print("- conversations ✅ (new)")
    print("- ai_insights ✅ (new)")
    print("- schedule_optimizations ✅ (new)")

if __name__ == "__main__":
    populate_missing_collections()
