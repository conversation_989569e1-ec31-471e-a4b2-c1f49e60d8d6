#!/usr/bin/env python3
"""
Firestore Database Setup Script
This script initializes the Firestore database with the required collections and indexes.
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv
import firebase_admin
from firebase_admin import credentials, firestore

# Load environment variables
load_dotenv()

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    if firebase_admin._apps:
        return firestore.client()
    
    service_account_path = os.getenv('FIREBASE_SERVICE_ACCOUNT_KEY_PATH')
    if not service_account_path or not os.path.exists(service_account_path):
        print("❌ Error: Firebase service account key not found!")
        print(f"Expected path: {service_account_path}")
        print("\nPlease:")
        print("1. Download your Firebase service account key")
        print("2. Update FIREBASE_SERVICE_ACCOUNT_KEY_PATH in .env")
        sys.exit(1)
    
    try:
        cred = credentials.Certificate(service_account_path)
        firebase_admin.initialize_app(cred)
        return firestore.client()
    except Exception as e:
        print(f"❌ Error initializing Firebase: {e}")
        sys.exit(1)

def create_sample_data(db):
    """Create sample data for testing"""
    print("📝 Creating sample data...")
    
    # Sample user
    sample_user = {
        'uid': 'demo_user_123',
        'first_name': 'Demo',
        'last_name': 'User',
        'email': '<EMAIL>',
        'date_of_birth': '1990-01-01',
        'created_at': datetime.utcnow().isoformat(),
        'updated_at': datetime.utcnow().isoformat()
    }
    
    db.collection('users').document('demo_user_123').set(sample_user)
    print("✅ Created sample user: demo_user_123")
    
    # Sample goal
    sample_goal = {
        'goal_id': 'demo_goal_123',
        'uid': 'demo_user_123',
        'title': 'Learn AI Development',
        'description': 'Master AI and machine learning concepts',
        'category': 'learning',
        'status': 'active',
        'created_at': datetime.utcnow().isoformat(),
        'updated_at': datetime.utcnow().isoformat()
    }
    
    db.collection('goals').document('demo_goal_123').set(sample_goal)
    print("✅ Created sample goal: Learn AI Development")
    
    # Sample task
    sample_task = {
        'task_id': 'demo_task_123',
        'uid': 'demo_user_123',
        'goal_id': 'demo_goal_123',
        'task_name': 'Complete Python ML Tutorial',
        'estimated_duration': 120,
        'flexibility': 'flexible',
        'importance': 4,
        'notes': 'Focus on scikit-learn basics',
        'status': 'planned',
        'categories': ['learning', 'programming'],
        'created_at': datetime.utcnow().isoformat(),
        'updated_at': datetime.utcnow().isoformat()
    }
    
    db.collection('tasks').document('demo_task_123').set(sample_task)
    print("✅ Created sample task: Complete Python ML Tutorial")

def setup_collections(db):
    """Set up Firestore collections with initial structure"""
    collections = [
        'users',
        'goals', 
        'tasks',
        'conversations',
        'ai_insights',
        'schedule_optimizations'
    ]
    
    print("🏗️  Setting up Firestore collections...")
    
    for collection_name in collections:
        # Create a temporary document to ensure collection exists
        temp_doc = {
            '_temp': True,
            'created_at': datetime.utcnow().isoformat(),
            'description': f'Temporary document to initialize {collection_name} collection'
        }
        
        doc_ref = db.collection(collection_name).document('_temp_init')
        doc_ref.set(temp_doc)
        print(f"✅ Initialized collection: {collection_name}")
    
    print("\n🧹 Cleaning up temporary documents...")
    # Clean up temporary documents
    for collection_name in collections:
        db.collection(collection_name).document('_temp_init').delete()
        print(f"🗑️  Removed temp document from: {collection_name}")

def create_indexes(db):
    """Create composite indexes for efficient queries"""
    print("\n📊 Note: Composite indexes need to be created manually in Firebase Console")
    print("Required indexes:")
    print("1. Collection: tasks")
    print("   Fields: uid (Ascending), status (Ascending)")
    print("   Query scope: Collection")
    print()
    print("2. Collection: goals") 
    print("   Fields: uid (Ascending), status (Ascending)")
    print("   Query scope: Collection")
    print()
    print("3. Collection: conversations")
    print("   Fields: uid (Ascending), timestamp (Descending)")
    print("   Query scope: Collection")
    print()
    print("4. Collection: ai_insights")
    print("   Fields: uid (Ascending), task_category (Ascending)")
    print("   Query scope: Collection")
    print()
    print("To create these indexes:")
    print("1. Go to Firebase Console > Firestore Database > Indexes")
    print("2. Click 'Create Index'")
    print("3. Add the fields as specified above")

def verify_setup(db):
    """Verify the database setup"""
    print("\n🔍 Verifying database setup...")
    
    try:
        # Test reading collections
        collections = ['users', 'goals', 'tasks', 'conversations', 'ai_insights', 'schedule_optimizations']
        
        for collection_name in collections:
            docs = db.collection(collection_name).limit(1).stream()
            doc_count = len(list(docs))
            print(f"✅ Collection '{collection_name}' is accessible")
        
        # Test sample data
        user_doc = db.collection('users').document('demo_user_123').get()
        if user_doc.exists:
            print("✅ Sample user data created successfully")
        
        goal_doc = db.collection('goals').document('demo_goal_123').get()
        if goal_doc.exists:
            print("✅ Sample goal data created successfully")
            
        task_doc = db.collection('tasks').document('demo_task_123').get()
        if task_doc.exists:
            print("✅ Sample task data created successfully")
        
        print("\n🎉 Database setup completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")

def main():
    """Main setup function"""
    print("🚀 Starting Firestore Database Setup")
    print("=" * 50)
    
    # Initialize Firebase
    db = initialize_firebase()
    print("✅ Firebase initialized successfully")
    
    # Setup collections
    setup_collections(db)
    
    # Create sample data
    create_sample_data(db)
    
    # Show index requirements
    create_indexes(db)
    
    # Verify setup
    verify_setup(db)
    
    print("\n" + "=" * 50)
    print("🎯 Next Steps:")
    print("1. Create the composite indexes in Firebase Console (see above)")
    print("2. Update your .env file with the correct Firebase credentials")
    print("3. Test your API endpoints with the sample data")
    print("4. Start building your frontend!")

if __name__ == "__main__":
    main()
