#!/usr/bin/env python3
"""
Simple test script to verify OpenAI API key works
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_openai_key():
    api_key = os.getenv('OPENAI_API_KEY')
    
    if not api_key:
        print("❌ No OPENAI_API_KEY found in .env file")
        return False
    
    if api_key.startswith('sk-'):
        print(f"✅ API key found: {api_key[:10]}...{api_key[-4:]}")
    else:
        print(f"⚠️  API key format looks unusual: {api_key[:10]}...")
    
    try:
        import requests
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "gpt-4o-mini",
            "messages": [{"role": "user", "content": "Say 'API test successful'"}],
            "max_tokens": 10
        }
        
        response = requests.post("https://api.openai.com/v1/chat/completions", 
                               headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()["choices"][0]["message"]["content"]
        print(f"✅ OpenAI API test successful: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API test failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_openai_key()
