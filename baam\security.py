"""
Security utilities for the scheduling app
"""
import time
import logging
from functools import wraps
from datetime import datetime, timedelta
from collections import OrderedDict
from flask import request, jsonify, make_response
from firebase_admin import auth

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NonceCache:
    """Cache for preventing replay attacks using nonces"""
    def __init__(self, max_size=10000):
        self.cache = OrderedDict()
        self.max_size = max_size
        self.expiry = timedelta(minutes=30)

    def add(self, nonce):
        # Remove expired nonces
        now = datetime.now()
        self.cache = OrderedDict(
            (k, v) for k, v in self.cache.items()
            if now - v < self.expiry
        )

        # Check cache size and remove oldest if needed
        if len(self.cache) >= self.max_size:
            self.cache.popitem(last=False)

        self.cache[nonce] = now

    def exists(self, nonce):
        return nonce in self.cache

# Global nonce cache instance
nonce_cache = NonceCache()

def validate_nonce(nonce):
    """Validate that nonce hasn't been used before"""
    if not nonce or nonce_cache.exists(nonce):
        return False
    nonce_cache.add(nonce)
    return True

def require_auth(f):
    """Decorator to require Firebase authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == 'OPTIONS':
            response = make_response()
            response.headers.add('Access-Control-Allow-Origin',
                                 request.headers.get('Origin', '*'))
            response.headers.add('Access-Control-Allow-Headers',
                                 'Content-Type,Authorization')
            response.headers.add('Access-Control-Allow-Methods',
                                 'GET,PUT,POST,DELETE,OPTIONS')
            response.headers.add('Access-Control-Allow-Credentials', 'true')
            return response

        # Allow test requests to bypass full auth with a special header
        test_uid = request.headers.get('X-Test-Auth-Uid')
        if test_uid:
            request.user = {'uid': test_uid, 'email': f'{test_uid}@test.com'}
            logger.info(f"Authenticated test user: {test_uid} for {request.endpoint}")
            return f(*args, **kwargs)

        auth_header = request.headers.get('Authorization')
        if not auth_header:
            logger.warning(f"No authorization token provided for {request.endpoint}")
            return jsonify({'error': 'No authorization token provided'}), 401

        try:
            token = auth_header.split('Bearer ')[1]
            decoded_token = auth.verify_id_token(token)

            if time.time() >= decoded_token['exp']:
                logger.warning(f"Expired token for user {decoded_token.get('uid', 'unknown')}")
                return jsonify({'error': 'Token has expired'}), 401

            request.user = decoded_token
            logger.info(f"Authenticated user: {decoded_token['uid']} for {request.endpoint}")
            return f(*args, **kwargs)
        except IndexError:
            logger.warning("Invalid token format")
            return jsonify({'error': 'Invalid token format'}), 401
        except auth.InvalidIdTokenError:
            logger.warning("Invalid Firebase token")
            return jsonify({'error': 'Invalid token'}), 401
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return jsonify({'error': str(e)}), 401

    return decorated_function

def require_security_headers(f):
    """Decorator to validate security headers and prevent replay attacks"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == 'OPTIONS':
            return f(*args, **kwargs)

        # Validate timestamp to prevent old requests
        timestamp = request.headers.get('X-Request-Timestamp')
        if timestamp:
            try:
                timestamp = int(timestamp)
                time_diff = abs(time.time() * 1000 - timestamp)
                if time_diff > 30000:  # 30 seconds
                    logger.warning(f"Request expired by {time_diff}ms from {request.remote_addr}")
                    return jsonify({'error': 'Request expired'}), 401
            except ValueError:
                logger.warning(f"Invalid timestamp format from {request.remote_addr}")
                return jsonify({'error': 'Invalid timestamp'}), 400

        # Check for replay attacks using nonce
        nonce = request.headers.get('X-Request-Nonce')
        if nonce and not validate_nonce(nonce):
            logger.warning(f"Invalid or reused nonce from {request.remote_addr}")
            return jsonify({'error': 'Invalid or reused nonce'}), 401

        return f(*args, **kwargs)

    return decorated_function

def get_combined_identifier():
    """Get combined user+IP identifier for rate limiting"""
    ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
    if hasattr(request, 'user') and request.user:
        return f"{request.user['uid']}:{ip}"
    return ip
